# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Docker
.dockerignore
docker-compose.override.yml

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
auth/__pycache__/
database/__pycache__/
database/database_fixed.py
model/__pycache__/
services/__pycache__/
services/pdf_processing_optimized.py
services/shared_vector_store.py
pdf
logs/
temp/
*.log

# Test files
api.md
static/minimal_websocket_test.html
static/smart_chat_jwt.html
static/test_smart_chat_jwt.html
static/pdf_processing_optimized.html
static/smart_chat.html
static/smart_chat_simple
test_*.py
app2.py
tests3.py
websocket_test*.html

# AWS
.aws/
aws-credentials.json
*.sh
!entrypoint.sh
*.ps1
task-definition*.json
original-task-definition*.json
appspec.yaml
buildspec.yml
aws-ecs-task-definition*.json
ecs-task-definition*.json
trust-policy.json
ecs-trust-policy.json
prasha-ai1-task-definition-v2.json
prasha-ai1-task-definition.json
aws-*.ps1
aws-*.sh
configure-aws.ps1
docker-build-run.ps1
run-docker-local*.ps1

# CI/CD
.github/workflows/*
!.github/workflows/README.md
.github/workflows/old-*.yml
.github/workflows/backup-*.yml
.github/workflows/aws-*.yml
.github/workflows/*.bak
.github/workflows/*.old
.github/workflows/deploy-*.yml
.github/workflows/ecs-*.yml

# Deployment files
DEPLOYMENT*.md
ALB-SETUP-README.md
MANUAL_DEPLOYMENT.md
PRASHA_AI1_DEPLOYMENT.md
AWS_CLI_COMMANDS_PRASHA_AI1.md
AWS_CONSOLE_DEPLOYMENT_PRASHA_AI1.md
cleanup*.ps1
cleanup*.sh

# Temporary files
*.tmp
*.bak
*.swp
*.swo
# temp_faiss_index/

# Local development
.python-version
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
.hypothesis/
.coverage
.coverage.*
coverage.xml
*.cover